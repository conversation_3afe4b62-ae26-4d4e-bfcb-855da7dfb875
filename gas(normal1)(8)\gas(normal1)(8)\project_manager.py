import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import shutil
from datetime import datetime
import traceback


class ProjectManager:
    """项目管理模块，负责项目保存、历史记录管理、设置按钮等功能"""
    
    def __init__(self, gas_calculator):
        """初始化项目管理器
        
        Args:
            gas_calculator: GasCalculator主类实例的引用
        """
        self.gas_calc = gas_calculator
        
    def backup_history_file(self):
        """创建历史文件的备份"""
        try:
            if os.path.exists(self.gas_calc.history_file):
                backup_dir = os.path.join(os.path.dirname(self.gas_calc.history_file), "备份")
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                today = datetime.now().strftime("%Y%m%d")
                backup_file = os.path.join(backup_dir, f"history_{today}.json")

                shutil.copy2(self.gas_calc.history_file, backup_file)
        except Exception as e:
            print(f"创建历史文件备份时出错: {str(e)}")
            
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.gas_calc.config_file):
                with open(self.gas_calc.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.gas_calc.custom_history_path = config.get('history_file_path', '')
            else:
                self.gas_calc.custom_history_path = ''
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
            self.gas_calc.custom_history_path = ''
            
    def save_config(self):
        """保存配置文件"""
        try:
            config = {}
            if os.path.exists(self.gas_calc.config_file):
                with open(self.gas_calc.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新历史文件路径
            config['history_file_path'] = self.gas_calc.custom_history_path if hasattr(self.gas_calc, 'custom_history_path') else ''

            # 保存配置文件
            with open(self.gas_calc.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")
            
    def center_window(self, window):
        """使窗口在屏幕中居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f'{width}x{height}+{x}+{y}')
        
    def save_air_data(self):
        """
        保存压缩空气计算数据到主程序中
        当压缩空气窗口打开时调用此方法以确保数据不丢失
        """
        try:
            if hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                # 收集当前压缩空气数据
                air_data = self.gas_calc.compressed_air_calculator.collect_data()

                # 获取当前项目标识
                project_name = self.gas_calc.project_name.get()
                project_code = self.gas_calc.project_code.get()
                current_project_id = f"{project_name}_{project_code}"

                # 初始化为字典的字典（如果需要）
                if not hasattr(self.gas_calc, 'saved_air_data'):
                    self.gas_calc.saved_air_data = {}
                elif not isinstance(self.gas_calc.saved_air_data, dict):
                    self.gas_calc.saved_air_data = {}

                # 以项目标识为键，存储压缩空气数据
                self.gas_calc.saved_air_data[current_project_id] = air_data
                print(f"压缩空气数据已保存到主程序，项目ID: {current_project_id}")
                return True
        except Exception as e:
            print(f"保存压缩空气数据时出错: {str(e)}")
            traceback.print_exc()
        return False
        
    def on_closing(self):
        """主窗口关闭事件处理"""
        try:

            # 确保压缩空气数据被保存
            if hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                try:
                    self.save_air_data()
                    print("主窗口关闭前已保存压缩空气数据")
                except Exception as e:
                    print(f"保存压缩空气数据时出错: {str(e)}")

            # 关闭所有子窗口
            self.close_all_windows()

            # 销毁主窗口
            self.gas_calc.root.destroy()

        except Exception as e:
            print(f"关闭主窗口时出错: {str(e)}")
            # 强制关闭
            self.gas_calc.root.destroy()
            
    def close_all_windows(self):
        """关闭所有打开的子窗口"""
        try:
            # 遍历所有打开的窗口并关闭
            for window_name, window in list(self.gas_calc.open_windows.items()):
                try:
                    if window and window.winfo_exists():
                        window.destroy()
                        print(f"已关闭窗口: {window_name}")
                except Exception as e:
                    print(f"关闭窗口 {window_name} 时出错: {str(e)}")

        # 清空窗口列表
        self.gas_calc.open_windows.clear()

    def save_project(self, show_message=True):
        """保存项目功能"""
        try:
            # 备份历史文件
            self.backup_history_file()

            # 获取压缩空气数据
            air_data = {}
            # 获取当前项目标识
            project_name = self.gas_calc.project_name.get()
            project_code = self.gas_calc.project_code.get()
            current_project_id = f"{project_name}_{project_code}"

            # 1. 首先尝试从打开的窗口获取最新数据
            if hasattr(self.gas_calc, 'air_window') and self.gas_calc.air_window and self.gas_calc.air_window.winfo_exists():
                try:
                    # 保存当前压缩空气数据到self.saved_air_data[current_project_id]
                    self.save_air_data()
                except Exception as e:
                    print(f"同步压缩空气窗口数据出错: {str(e)}")
                    traceback.print_exc()

            # 2. 使用保存的数据或直接从计算器获取
            if hasattr(self.gas_calc, 'saved_air_data') and isinstance(self.gas_calc.saved_air_data, dict) and current_project_id in self.gas_calc.saved_air_data:
                air_data = self.gas_calc.saved_air_data[current_project_id]
                print(f"使用已保存的{current_project_id}项目的压缩空气数据")
            elif hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                try:
                    air_data = self.gas_calc.compressed_air_calculator.collect_data()
                    # 确保数据与当前项目关联
                    if hasattr(self.gas_calc, 'saved_air_data'):
                        if not isinstance(self.gas_calc.saved_air_data, dict):
                            self.gas_calc.saved_air_data = {}
                        self.gas_calc.saved_air_data[current_project_id] = air_data
                    print(f"已重新获取{current_project_id}项目的压缩空气数据")
                except Exception as e:
                    print(f"获取压缩空气数据时出错: {str(e)}")
                    traceback.print_exc()


            # 收集项目数据
            project_data = {
                "工程名称": self.gas_calc.project_name.get(),
                "工程代号": self.gas_calc.project_code.get(),
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),

            }

            # 合并压缩空气数据
            if air_data:
                for key, value in air_data.items():
                    project_data[key] = value

        except Exception as e:
            print(f"保存项目数据时出错: {str(e)}")
            traceback.print_exc()
            if show_message:
                messagebox.showerror("保存失败", f"保存项目数据时出错: {str(e)}")
            return False

        # 收集其他数据
        try:
            # 获取氧枪数据
            if hasattr(self.gas_calc, 'has_oxygen_lance') and self.gas_calc.has_oxygen_lance.get() == "是":
                if hasattr(self.gas_calc, 'oxygen_lance_calculator') and self.gas_calc.oxygen_lance_calculator:
                    try:
                        o2_data, ng_data = self.gas_calc.oxygen_lance_calculator.collect_data()
                        project_data["氧枪氧气数据"] = o2_data
                        project_data["氧枪天然气数据"] = ng_data

                        # 使用"氧枪_"前缀保存氧气数据
                        for key, value in o2_data.items():
                            project_data[f"氧枪_氧气{key}"] = value

                        # 使用"氧枪_"前缀保存天然气数据
                        for key, value in ng_data.items():
                            project_data[f"氧枪_天然气{key}"] = value
                    except Exception as e:
                        print(f"收集氧枪数据时出错: {str(e)}")

            # 收集天然气数据
            if hasattr(self.gas_calc, 'has_natural_gas') and self.gas_calc.has_natural_gas.get() == "是":
                project_data["天然气总管调节阀前压力(kPa)"] = self.gas_calc.ng_main_valve_pre.get()
                project_data["天然气总管调节阀后压力(kPa)"] = self.gas_calc.ng_main_valve_post.get()
                project_data["天然气支管调节阀前压力(kPa)"] = self.gas_calc.ng_branch_valve_pre.get()
                project_data["天然气支管调节阀后压力(kPa)"] = self.gas_calc.ng_branch_valve_post.get()
                project_data["天然气支管K"] = self.gas_calc.ng_branch_k.get()

            # 收集自力式阀数据
            if hasattr(self.gas_calc, 'valve_count'):
                for i in range(1, int(self.gas_calc.valve_count.get()) + 1):
                    if hasattr(self.gas_calc, f"valve{i}_description"):
                        project_data[f"自力式阀{i}描述"] = getattr(self.gas_calc, f"valve{i}_description").get()
                        project_data[f"自力式阀{i}最大流量(Nm³/h)"] = getattr(self.gas_calc, f"valve{i}_max_flow").get()
                        project_data[f"自力式阀{i}介质"] = getattr(self.gas_calc, f"valve{i}_medium").get()
                        project_data[f"自力式阀{i}阀前压力(kPa)"] = getattr(self.gas_calc, f"valve{i}_pre_pressure").get()
                        project_data[f"自力式阀{i}阀后压力(kPa)"] = getattr(self.gas_calc, f"valve{i}_post_pressure").get()
                        project_data[f"自力式阀{i}开度K(%)"] = getattr(self.gas_calc, f"valve{i}_k_percent").get()

            # 收集小炉数据
            if hasattr(self.gas_calc, 'furnace_data'):
                for furnace in self.gas_calc.furnace_data:
                    furnace_data = {
                        "平均热负荷": furnace['heat_load'].get(),
                        "浮动值": furnace['float_value'].get(),
                        "喷枪数": furnace['nozzle_count'].get(),
                        "选取管径": furnace['selected_diameter'].get(),
                        "选取阀后管径": furnace['selected_post_diameter'].get()
                    }
                    project_data[f"小炉{len(self.gas_calc.furnace_data)}数据"] = furnace_data

        except Exception as e:
            print(f"收集其他数据时出错: {str(e)}")

        # 保存到历史文件
        try:
            # 读取现有历史记录
            if os.path.exists(self.gas_calc.history_file):
                with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []

            # 检查是否存在相同工程名称和工程代号的记录
            found_index = -1
            project_name = self.gas_calc.project_name.get()
            project_code = self.gas_calc.project_code.get()

            for i, record in enumerate(history):
                if record.get("工程名称") == project_name:
                    found_index = i
                    break
                if project_code and record.get("工程代号") == project_code:
                    found_index = i
                    break

            # 更新或添加记录
            if found_index >= 0:
                if show_message:
                    result = messagebox.askyesno("确认", f"工程名称 '{project_name}' 已存在，是否覆盖？")
                    if not result:
                        return False
                history[found_index] = project_data
            else:
                history.append(project_data)

            # 保存更新后的历史记录
            with open(self.gas_calc.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            # 更新历史记录显示
            if hasattr(self.gas_calc, 'update_history_display'):
                self.gas_calc.update_history_display()

            if show_message:
                messagebox.showinfo("保存成功", f"项目 '{project_name}' 已保存到历史记录")
            return True

        except Exception as e:
            print(f"保存到历史文件时出错: {str(e)}")
            if show_message:
                messagebox.showerror("保存失败", f"保存到历史文件时出错: {str(e)}")
            return False

    def save_project_silent(self):
        """静默保存项目，不显示消息框"""
        return self.save_project(show_message=False)

    def load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.gas_calc.history_file):
                with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                    self.gas_calc.history = json.load(f)
            else:
                self.gas_calc.history = []
        except Exception as e:
            print(f"加载历史记录时出错: {str(e)}")
            self.gas_calc.history = []

    def update_history_display(self):
        """更新历史记录显示"""
        try:
            if hasattr(self.gas_calc, 'history_tree') and self.gas_calc.history_tree:
                # 清空现有项目
                for item in self.gas_calc.history_tree.get_children():
                    self.gas_calc.history_tree.delete(item)

                # 添加历史记录
                for record in self.gas_calc.history:
                    time_str = record.get("时间", "")
                    project_name = record.get("工程名称", "")
                    project_code = record.get("工程代号", "")

                    self.gas_calc.history_tree.insert("", "end", values=(time_str, project_name, project_code))

        except Exception as e:
            print(f"更新历史记录显示时出错: {str(e)}")

    def delete_record(self):
        """删除选中的历史记录"""
        try:
            if not hasattr(self.gas_calc, 'history_tree') or not self.gas_calc.history_tree:
                return

            selected = self.gas_calc.history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择要删除的记录")
                return

            # 确认删除
            result = messagebox.askyesno("确认删除", "确定要删除选中的记录吗？")
            if not result:
                return

            # 获取选中记录的信息
            values = self.gas_calc.history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return

            # 从历史数据中删除匹配的记录
            for i, record in enumerate(self.gas_calc.history):
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):

                    del self.gas_calc.history[i]
                    break

            # 保存更新后的历史记录
            with open(self.gas_calc.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.gas_calc.history, f, ensure_ascii=False, indent=2)

            # 更新显示
            self.update_history_display()
            messagebox.showinfo("删除成功", "记录已删除")

        except Exception as e:
            messagebox.showerror("删除失败", f"删除记录时出错: {str(e)}")

    def search_history(self):
        """搜索历史记录"""
        try:
            if not hasattr(self.gas_calc, 'search_var') or not hasattr(self.gas_calc, 'history_tree'):
                return

            search_text = self.gas_calc.search_var.get().lower()

            # 清空现有项目
            for item in self.gas_calc.history_tree.get_children():
                self.gas_calc.history_tree.delete(item)

            # 添加匹配的历史记录
            for record in self.gas_calc.history:
                time_str = record.get("时间", "")
                project_name = record.get("工程名称", "")
                project_code = record.get("工程代号", "")

                # 检查是否匹配搜索条件
                if (search_text in time_str.lower() or
                    search_text in project_name.lower() or
                    search_text in project_code.lower()):

                    self.gas_calc.history_tree.insert("", "end", values=(time_str, project_name, project_code))

        except Exception as e:
            print(f"搜索历史记录时出错: {str(e)}")

    def clear_search(self):
        """清空搜索"""
        try:
            if hasattr(self.gas_calc, 'search_var'):
                self.gas_calc.search_var.set("")
            self.update_history_display()
        except Exception as e:
            print(f"清空搜索时出错: {str(e)}")
